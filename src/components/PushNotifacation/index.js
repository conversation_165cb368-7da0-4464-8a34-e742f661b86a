import { useEffect } from "react";
import {
  <PERSON>ert,
  PermissionsAndroid,
  StatusBar,
  StyleSheet,
  Text,
  useColorScheme,
  View,
} from "react-native";
import messaging from "@react-native-firebase/messaging";
function PushNotifacation() {
  const requestPermission = async () => {
    try {
      const result = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
      );
      console.log("result**", result);
      console.log("result**2", PermissionsAndroid.RESULTS.GRANTED);
      if (result === PermissionsAndroid.RESULTS.GRANTED) {
        // request for device token
        requestToken();
      } else {
        Alert.alert("Permission Denied");
      }
    } catch (error) {
      console.log(error);
    }
  };

  const requestToken = async () => {
    try {
      // Check if already registered for remote messages
      const isRegistered = messaging().isDeviceRegisteredForRemoteMessages;
      console.log("Device registered for remote messages:", isRegistered);

      if (!isRegistered) {
        console.log("Registering device for remote messages...");
        await messaging().registerDeviceForRemoteMessages();
        console.log("Successfully registered for remote messages");
      }

      const token = await messaging().getToken();
      console.log("FCM Token:", token);

      if (token) {
        // You can send this token to your server here
        console.log("Token obtained successfully");
      } else {
        console.log("No token received");
      }
    } catch (error) {
      console.log("Error getting token:", error);

      // If registration fails, try to get token anyway (might work on simulator)
      try {
        const token = await messaging().getToken();
        if (token) {
          console.log("Token obtained without registration:", token);
        }
      } catch (tokenError) {
        console.log("Failed to get token:", tokenError);
      }
    }
  };

  useEffect(() => {
    requestPermission();
  }, []);

  /** foreground notification */

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      Alert.alert("A new FCM message arrived!", JSON.stringify(remoteMessage));
    });

    return unsubscribe;
  }, []);

  return (
    <View style={styles.container}>
      <Text>Push Notification</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default PushNotifacation;
