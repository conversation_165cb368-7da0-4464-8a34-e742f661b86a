import { useEffect } from "react";
import { Alert, PermissionsAndroid, Platform } from "react-native";
import messaging from "@react-native-firebase/messaging";

function PushNotice() {
  const requestPermission = async () => {
    try {
      let authStatus;

      if (Platform.OS === "ios") {
        // Request permission for iOS using Firebase Messaging
        authStatus = await messaging().requestPermission({
          alert: true,
          announcement: false,
          badge: true,
          carPlay: true,
          provisional: false,
          sound: true,
        });

        console.log("iOS Authorization status:", authStatus);

        // Check if permission is granted
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log("iOS Authorization status:", authStatus);
          requestToken();
        } else {
          Alert.alert(
            "Permission Denied",
            "Please enable notifications in Settings"
          );
        }
      } else {
        // Request permission for Android
        const result = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        console.log("Android result:", result);

        if (result === PermissionsAndroid.RESULTS.GRANTED) {
          requestToken();
        } else {
          Alert.alert("Permission Denied");
        }
      }
    } catch (error) {
      console.log("Permission request error:", error);
      Alert.alert("Error", "Failed to request notification permission");
    }
  };

  const requestToken = async () => {
    try {
      // Check if already registered for remote messages
      const isRegistered = messaging().isDeviceRegisteredForRemoteMessages;
      console.log("Device registered for remote messages:", isRegistered);

      if (!isRegistered) {
        console.log("Registering device for remote messages...");
        await messaging().registerDeviceForRemoteMessages();
        console.log("Successfully registered for remote messages");
      }

      const token = await messaging().getToken();
      console.log("FCM Token:", token);

      if (token) {
        // You can send this token to your server here
        console.log("Token obtained successfully");
      } else {
        console.log("No token received");
      }
    } catch (error) {
      console.log("Error getting token:", error);

      // If registration fails, try to get token anyway (might work on simulator)
      try {
        const token = await messaging().getToken();
        if (token) {
          console.log("Token obtained without registration:", token);
        }
      } catch (tokenError) {
        console.log("Failed to get token:", tokenError);
      }
    }
  };

  useEffect(() => {
    requestPermission();
  }, []);

  /** foreground notification */

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      Alert.alert(
        remoteMessage.notification.title,
        remoteMessage.notification.body
      );
    });

    return unsubscribe;
  }, []);

  return null;
}

export default PushNotice;
